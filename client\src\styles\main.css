/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette - Modern & Elegant */
    --primary-color: #06d6a0;
    --primary-color-rgb: 6, 214, 160;
    --primary-dark: #05b085;
    --secondary-color: #118ab2;
    --accent-color: #ffd166;
    --danger-color: #f72585;
    --warning-color: #f77f00;

    /* Neutral Colors - Softer Dark Theme */
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a24;
    --bg-tertiary: #252538;
    --surface: #2a2a3e;
    --surface-hover: #3a3a54;
    --surface-primary: #f8fafc;
    --surface-secondary: #f1f5f9;
    --background-primary: #ffffff;
    
    /* Text Colors */
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #94a3b8;
    --text-accent: #06d6a0;
    
    /* Borders and Shadows */
    --border-color: #3a3a54;
    --border-light: #e5e7eb;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-12: 3rem;
    
    /* Transitions */
    --transition: all 0.2s ease-in-out;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: var(--font-size-base);
}

/* Layout Components */
#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 确保连接状态区域有足够空间 */
.connection-status {
    min-width: 120px;
    display: flex;
    justify-content: flex-end;
}

/* Header */
.header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-4) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

/* 修复header布局 */
.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap; /* 防止换行 */
}

.logo h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-accent);
    margin-bottom: var(--spacing-1);
}

.subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
}

/* Connection Status */
.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: var(--transition);
    white-space: nowrap; /* 防止文字换行 */
    min-width: 80px; /* 确保最小宽度 */
}

.status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
    flex-shrink: 0; /* 防止圆点被压缩 */
}

.status-indicator.connected {
    background: rgba(16, 185, 129, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-indicator.connecting {
    background: rgba(249, 158, 11, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(249, 158, 11, 0.3);
}

.status-indicator.disconnected {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main Content */
.main-content {
    flex: 1;
    width: 120vh;
    margin: auto;
    padding: var(--spacing-6);
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: var(--spacing-6);
    grid-template-areas:
        "controls audio"
        "conversation conversation";
    min-height: 0;
}

.control-panel {
    grid-area: controls;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-8);
    border: 1px solid var(--border-color);
    min-height: 0;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.audio-section {
    grid-area: audio;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    min-height: 0; /* 修复：防止内容溢出 */
}

.conversation-section {
    grid-area: conversation;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-6);
    border: 1px solid var(--border-color);
    min-height: 400px;
    max-height: 600px; /* 修复：限制最大高度 */
    display: flex;
    flex-direction: column;
}

.stats-panel {
    grid-area: stats;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-6);
    border: 1px solid var(--border-color);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-areas:
            "controls"
            "audio"
            "conversation";
        gap: var(--spacing-4);
    }

    .control-panel,
    .audio-section,
    .conversation-section {
        padding: var(--spacing-4);
    }
}

@media (max-width: 768px) {
    /* 修复header在小屏幕的显示 */
    .header-content {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: center;
        text-align: center;
    }
    
    .connection-status {
        justify-content: center;
        min-width: auto;
        width: 100%;
    }
    
    .status-indicator {
        justify-content: center;
        width: 100%;
        max-width: 200px;
    }
    
    /* 修复主内容区域 */
    .main-content {
        padding: var(--spacing-4);
        gap: var(--spacing-3);
    }
    
    /* 修复模式按钮 */
    .mode-buttons {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .mode-btn {
        flex: none;
        min-width: auto;
        width: 100%;
    }
    
    /* 修复音频设置 */
    .setting-group {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-2);
    }
    
    .setting-group label {
        min-width: auto;
        text-align: left;
    }
    
    /* 修复统计面板 */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-2);
    }
    
    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-1);
    }
    
    .stat-label {
        margin-right: 0;
    }
    
    /* 修复对话区域 */
    .conversation-section {
        min-height: 300px;
        max-height: 450px;
    }
    
    .chat-container {
        max-height: 400px;
        min-height: 200px;
    }
    
    .chat-message {
        max-width: 95%;
    }
    
    /* 修复footer */
    .footer-content {
        flex-direction: column;
        gap: var(--spacing-2);
        text-align: center;
    }
    
    .footer-links {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* Panel Headers */
h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

/* 对话控制区域 */
.conversation-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-4);
}

.main-control {
    display: flex;
    justify-content: center;
}

/* 主要对话按钮 */
.conversation-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-primary);
    border: none;
    border-radius: 20px;
    padding: var(--spacing-6) var(--spacing-8);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: var(--font-size-lg);
    font-weight: 600;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    min-width: 200px;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.conversation-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 25px -5px rgba(16, 185, 129, 0.3);
}

.conversation-btn:active {
    transform: translateY(0);
}

.conversation-btn .btn-icon {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-1);
}

.conversation-btn .btn-text {
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.conversation-btn .btn-status {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    font-weight: 400;
}

/* 不同状态的按钮样式 */
.conversation-btn[data-state="mode-required"] {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    cursor: not-allowed;
    opacity: 0.7;
}

.conversation-btn[data-state="mode-required"]:hover {
    transform: none;
    box-shadow: var(--shadow-lg);
}

.conversation-btn[data-state="disconnected"] {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.conversation-btn[data-state="connecting"] {
    background: linear-gradient(135deg, var(--warning-color), #ea580c);
    animation: pulse-warning 2s infinite;
}

.conversation-btn[data-state="connected"] {
    background: linear-gradient(135deg, var(--secondary-color), #2563eb);
}

.conversation-btn[data-state="recording"] {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    animation: recording-pulse 1.5s infinite;
}

@keyframes pulse-warning {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes recording-pulse {
    0%, 100% {
        box-shadow: 0 15px 25px -5px rgba(239, 68, 68, 0.3);
    }
    50% {
        box-shadow: 0 15px 25px -5px rgba(239, 68, 68, 0.6);
    }
}

/* Mode Selection */
.mode-selection {
    border-radius: 12px;
    padding: var(--spacing-6);
    width: 100%;
    max-width: 500px;
    animation: slideDown 0.3s ease;
    margin-bottom: 30px;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mode-selection h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-lg);
    font-weight: 600;
    text-align: center;
}

.mode-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
}

.mode-btn {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-4);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.mode-btn:hover {
    background: var(--surface-hover);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.mode-btn.active {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
    color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.mode-btn .mode-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-1);
}

.mode-btn .mode-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-1);
}

.mode-btn .mode-desc {
    font-size: var(--font-size-xs);
    opacity: 0.8;
    line-height: 1.4;
}

/* Control Buttons */
.control-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-6);
}

.primary-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.primary-btn:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.primary-btn:disabled {
    background: var(--surface);
    color: var(--text-muted);
    cursor: not-allowed;
}

.secondary-btn {
    background: var(--surface);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.secondary-btn:hover:not(:disabled) {
    background: var(--surface-hover);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.secondary-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Audio Settings */
.audio-settings {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

/* 修复音频设置布局 */
.setting-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
    flex-wrap: nowrap; /* 防止设置项换行 */
}

.setting-group label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    min-width: 80px;
    white-space: nowrap; /* 防止标签文字换行 */
}

.setting-group input[type="range"] {
    flex: 1;
    accent-color: var(--primary-color);
}

.setting-group select {
    flex: 1;
    background: var(--surface);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

#volume-value {
    min-width: 30px;
    font-size: var(--font-size-sm);
    color: var(--text-accent);
    font-weight: 500;
}

/* Audio Panels */
.audio-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
    border: 1px solid var(--border-color);
}

.waveform {
    height: 80px;
    background: var(--surface);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-3);
    position: relative;
    overflow: hidden;
}

.waveform::after {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.audio-info {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Volume Meter */
.volume-meter {
    width: 100%;
    height: 20px;
    background: var(--surface);
    border-radius: var(--border-radius);
    overflow: hidden;
    position: relative;
    border: 1px solid var(--border-color);
}

.volume-bar {
    height: 100%;
    width: 0%;
    background: var(--primary-color);
    transition: width 0.1s ease-out;
    border-radius: var(--border-radius);
}

/* Audio Visualizer */
.audio-visualizer {
    width: 100%;
    height: 100px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.audio-visualizer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.audio-visualizer canvas {
    border-radius: 8px;
    background: transparent;
}

/* Conversation Display */
.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.clear-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: var(--transition);
}

.clear-btn:hover {
    background: #dc2626;
}

/* 对话显示修复 */
.conversation-display {
    flex: 1;
    max-height: none; /* 修复：移除固定高度 */
    overflow-y: auto;
    background: var(--surface);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
    min-height: 0; /* 修复：允许收缩 */
}

.welcome-message {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-8);
}

.message {
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-3);
    border-radius: var(--border-radius);
    position: relative;
    word-wrap: break-word;
}

.message.user {
    background: rgba(59, 130, 246, 0.1);
    border-left: 3px solid var(--secondary-color);
    margin-right: var(--spacing-6);
}

.message.assistant {
    background: rgba(16, 185, 129, 0.1);
    border-left: 3px solid var(--primary-color);
    margin-left: var(--spacing-6);
}

.message.system {
    background: rgba(156, 163, 175, 0.1);
    border-left: 3px solid #9ca3af;
    margin: 0 var(--spacing-3);
    font-style: italic;
    text-align: center;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.message-role {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.message-time {
    font-size: var(--font-size-xs);
}

.message-content {
    color: var(--text-primary);
    width: auto;
}

/* Statistics Panel */
/* 修复统计面板布局 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-4);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3);
    background: var(--surface);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    flex-wrap: nowrap; /* 修复：防止统计项内容换行 */
    min-width: 0; /* 修复：允许收缩 */
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-right: var(--spacing-2);
    white-space: nowrap; /* 修复：防止标签换行 */
    overflow: hidden;
    text-overflow: ellipsis; /* 修复：长文本省略 */
}

.stat-item span:last-child {
    font-weight: 600;
    color: var(--text-accent);
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-4) 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.footer-links {
    display: flex;
    gap: var(--spacing-4);
}

.footer-links a {
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    transition: var(--transition);
}

.modal.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.close-btn:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-6);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--surface-hover);
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message {
    animation: slideIn 0.3s ease-out;
}

/* Loading States */
.loading {
    position: relative;
    color: transparent !important;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-success {
    color: var(--primary-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

/* Enhanced Chat Interface Styles */

/* Chat Container */
.chat-container {
    grid-area: conversation;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-6);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    min-height: 500px;
    max-height: 600px;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-3);
}

.chat-messages {
    height: 400px;
    overflow-y: auto;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-input-area {
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-3);
}

/* Chat Messages - 修复布局和对齐问题 */
.chat-message {
  margin-bottom: var(--spacing-4);
  animation: messageSlideIn 0.3s ease-out;
  max-width: 85%;
  clear: both; /* 确保消息不会重叠 */
}

.chat-message.message-user {
  margin-left: auto;
  margin-right: 0;
  float: right; /* 用户消息右对齐 */
}

.chat-message.message-assistant {
  margin-left: 0;
  margin-right: auto;
  float: left; /* AI消息左对齐 */
}

.chat-message.message-system {
  margin: 0 auto var(--spacing-4) auto;
  max-width: 70%;
  text-align: center;
  float: none; /* 系统消息居中 */
}

.message-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.message-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 12px;
}

.message.user .message-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.message.assistant .message-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.message.system .message-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.message-sender {
    font-weight: 500;
    color: var(--text-primary);
}

.message-time {
    margin-left: auto;
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.message-body {
    background: var(--surface-hover);
    border-radius: var(--border-radius);
    padding: var(--spacing-3);
    position: relative;
}

.chat-message.message-user .message-body {
    background: rgba(59, 130, 246, 0.15);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.chat-message.message-assistant .message-body {
    background: rgba(16, 185, 129, 0.15);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.chat-message.message-system .message-body {
    background: rgba(156, 163, 175, 0.15);
    border: 1px solid rgba(156, 163, 175, 0.3);
    font-style: italic;
}

.message-content {
    color: var(--text-primary);
    word-wrap: break-word;
    white-space: pre-wrap;
}

.message.streaming .message-content {
    border-color: #f97316;
    background: rgba(249, 115, 22, 0.1);
    position: relative;
}

/* 🎯 移除旧的流式指示器，使用新的光标效果 */

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: var(--font-size-xs);
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 12px;
    margin-left: var(--spacing-2);
}

.status-indicator.typing {
    background: rgba(249, 158, 11, 0.2);
    color: var(--accent-color);
}

.status-indicator.streaming {
    background: rgba(16, 185, 129, 0.2);
    color: var(--primary-color);
    animation: pulse 1.5s infinite;
}

.status-indicator.playing {
    background: rgba(139, 69, 19, 0.2);
    color: #8b4513;
    animation: pulse 1s infinite;
}

.status-indicator.error {
    background: rgba(239, 68, 68, 0.2);
    color: var(--danger-color);
}

/* Typing Animation */
.typing-animation {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: var(--spacing-2) 0;
}

.typing-animation span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-muted);
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(1) { animation-delay: -0.32s; }
.typing-animation span:nth-child(2) { animation-delay: -0.16s; }

.streaming-placeholder {
    color: var(--text-muted);
    font-style: italic;
    padding: var(--spacing-1) 0;
}

/* 🎯 新增：流式文本效果样式 */
.streaming-text {
    display: inline;
    transition: opacity 0.2s ease-in-out;
}

.streaming-text-container {
    display: inline;
}

.message-streaming .message-content {
    position: relative;
}

/* 🎯 流式文本光标样式 */
.streaming-cursor {
    display: inline;
    color: var(--primary-color);
    font-weight: bold;
    margin-left: 2px;
    font-size: 1em;
    line-height: 1;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 🎯 增强流式消息的视觉效果 */
.message-streaming .message-content {
    border-left: 3px solid var(--primary-color);
    background: rgba(var(--primary-color-rgb), 0.05);
    animation: streamingGlow 2s ease-in-out infinite alternate;
}

@keyframes streamingGlow {
    0% { box-shadow: 0 0 5px rgba(var(--primary-color-rgb), 0.3); }
    100% { box-shadow: 0 0 15px rgba(var(--primary-color-rgb), 0.6); }
}

/* 🎯 转录状态容器样式 */
.transcript-status-container {
    margin: var(--spacing-4) 0;
    padding: var(--spacing-4);
    background: var(--surface-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-panel {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    background: var(--surface-primary);
    border-radius: 8px;
    border: 1px solid var(--border-light);
}

.status-label {
    font-weight: 500;
    color: var(--text-secondary);
    min-width: 100px;
    flex-shrink: 0;
}

.status-text {
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    flex: 1;
    padding: var(--spacing-1) var(--spacing-2);
    background: rgba(var(--primary-color-rgb), 0.05);
    border-radius: 4px;
    border: 1px solid rgba(var(--primary-color-rgb), 0.1);
    transition: all 0.2s ease;
}

.status-text.active {
    background: rgba(var(--primary-color-rgb), 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
}

/* 🎯 错误容器样式 */
.error-container {
    margin: var(--spacing-4) 0;
    padding: 0;
}

.error-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    color: #dc2626;
    margin-bottom: var(--spacing-2);
}

.error-icon {
    font-size: 1.2em;
    flex-shrink: 0;
}

.error-text {
    flex: 1;
    font-weight: 500;
}

.error-dismiss {
    background: none;
    border: none;
    color: #dc2626;
    font-size: 1.5em;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.error-dismiss:hover {
    background: rgba(239, 68, 68, 0.1);
}

/* Audio Controls */
.audio-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-top: var(--spacing-2);
    padding-top: var(--spacing-2);
    border-top: 1px solid var(--border-color);
}

.audio-play-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.audio-play-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.audio-duration {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

/* Current Status Area */
.current-status {
    background: var(--surface);
    border-radius: var(--border-radius);
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) 0;
    font-size: var(--font-size-sm);
}

.status-item:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
}

.status-icon {
    font-size: var(--font-size-base);
}

.status-text {
    color: var(--text-secondary);
    flex: 1;
}

/* System Message Styling */
.system-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
}

.system-message .message-icon {
    font-size: var(--font-size-2xl);
}

.system-message .message-content {
    text-align: center;
}

.system-message .message-content p {
    margin-bottom: var(--spacing-2);
}

.system-message .message-content p:last-child {
    margin-bottom: 0;
}

/* Message State Styling */
.message-typing .message-body {
    border: 2px dashed rgba(249, 158, 11, 0.5);
    animation: breathe 2s infinite;
}

.message-streaming .message-body {
    border: 2px dashed rgba(16, 185, 129, 0.5);
    animation: breathe 2s infinite;
}

.message-playing .message-body {
    border: 2px solid rgba(139, 69, 19, 0.5);
    box-shadow: 0 0 10px rgba(139, 69, 19, 0.3);
}

.message-error .message-body {
    border: 2px solid rgba(239, 68, 68, 0.5);
    background: rgba(239, 68, 68, 0.1);
}

/* Enhanced Animations */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes breathe {
    0%, 100% {
        border-color: rgba(16, 185, 129, 0.3);
    }
    50% {
        border-color: rgba(16, 185, 129, 0.7);
    }
}

/* Responsive Chat Design */
@media (max-width: 768px) {
    .chat-container {
        max-height: 400px;
        min-height: 300px;
    }
    
    .chat-message {
        max-width: 95%;
    }
    
    .message-body {
        padding: var(--spacing-2);
    }
    
    .message-header {
        font-size: var(--font-size-xs);
    }
}

/* Scroll styling for chat messages */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--surface);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
} 

/* 音频可视化改进 */
.waveform {
    width: 100%;
    height: 60px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.9em;
    position: relative;
    overflow: hidden;
}

.waveform.active {
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.2), rgba(249, 115, 22, 0.2));
}

.waveform.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: wave 2s infinite;
}

@keyframes wave {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 音量计改进 */
.volume-meter {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    margin: 10px 0;
    overflow: hidden;
    position: relative;
}

.volume-meter .volume-bar {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #f97316, #ef4444);
    border-radius: 4px;
    transition: width 0.1s ease-out;
    position: relative;
}

.volume-meter .volume-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 音频信息显示改进 */
.audio-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 0.9em;
    color: var(--text-secondary);
}

.audio-info span {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    backdrop-filter: blur(4px);
}

/* 实时状态指示器 */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
}

.status-indicator.connected {
    background: #10b981;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-indicator.connecting {
    background: #f97316;
    animation: pulse 1.5s infinite;
}

.status-indicator.disconnected {
    background: #ef4444;
}

.status-indicator.connected::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 1px solid #10b981;
    border-radius: 50%;
    animation: ripple 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes ripple {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
} 

/* 添加响应式修复 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: stretch;
    }
    
    .connection-status {
        justify-content: center;
        min-width: auto;
    }
    
    .mode-buttons {
        flex-direction: column;
    }
    
    .mode-btn {
        min-width: auto;
    }
}

/* 🎯 新增：合作型附和样式 */
#aiStatus.interjection {
    color: #667eea !important; /* 使用紫蓝色来区别于普通状态 */
    font-style: italic;
    font-weight: 500;
    transition: opacity 0.3s ease-in-out;
    background: rgba(102, 126, 234, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    border-left: 3px solid #667eea;
} 

/* Add any additional application styles here */

/* --- 新增：合作型附和样式 --- */
.cooperative-interjection {
  position: absolute;
  bottom: -10px;
  right: 20px;
  background-color: var(--secondary-bg);
  color: var(--text-color);
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid var(--border-color);
  opacity: 0;
  transform: translateY(10px) scale(0.9);
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 10;
  pointer-events: none; /* 确保它不会干扰鼠标事件 */
}

.cooperative-interjection.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 确保聊天消息容器有相对定位 */
.chat-message {
  position: relative;
} 